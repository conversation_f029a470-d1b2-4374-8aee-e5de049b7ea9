import { PublicKey, Transaction, Connection } from '@solana/web3.js';
import { solanaConnection } from './connection';
import { transferCopyTokens, getTokenBalance } from './token-utils';
import { CONTRACTS } from '@/lib/web3/config';

/**
 * Top-up service for handling $COPY token deposits
 */

export interface TopUpRequest {
  userWallet: PublicKey;
  amount: number;
  userId: string;
}

export interface TopUpResult {
  success: boolean;
  transactionSignature?: string;
  error?: string;
  amount?: number;
  recipientAddress?: string;
}

export interface TopUpValidation {
  isValid: boolean;
  error?: string;
  minAmount?: number;
  maxAmount?: number;
  userBalance?: number;
}

/**
 * Validate top-up request
 */
export async function validateTopUpRequest(
  userWallet: PublicKey,
  amount: number,
  connection?: Connection
): Promise<TopUpValidation> {
  const conn = connection || solanaConnection;
  
  console.log('🔍 Validating top-up request:', {
    userWallet: userWallet.toString(),
    amount,
  });

  try {
    // Get environment limits
    const minAmount = parseFloat(process.env.TOPUP_MIN_AMOUNT || '1');
    const maxAmount = parseFloat(process.env.TOPUP_MAX_AMOUNT || '10000');

    // Validate amount range
    if (amount < minAmount) {
      return {
        isValid: false,
        error: `Minimum top-up amount is ${minAmount} $COPY`,
        minAmount,
        maxAmount,
      };
    }

    if (amount > maxAmount) {
      return {
        isValid: false,
        error: `Maximum top-up amount is ${maxAmount} $COPY`,
        minAmount,
        maxAmount,
      };
    }

    // Check user's token balance
    const userBalance = await getTokenBalance(
      userWallet,
      CONTRACTS.COPY_TOKEN,
      conn
    );

    console.log('💰 User token balance:', {
      userWallet: userWallet.toString(),
      balance: userBalance,
      requestedAmount: amount,
    });

    if (userBalance < amount) {
      return {
        isValid: false,
        error: `Insufficient balance. You have ${userBalance} $COPY, but need ${amount} $COPY`,
        minAmount,
        maxAmount,
        userBalance,
      };
    }

    return {
      isValid: true,
      minAmount,
      maxAmount,
      userBalance,
    };
  } catch (error) {
    console.error('❌ Error validating top-up request:', error);
    return {
      isValid: false,
      error: 'Failed to validate top-up request. Please try again.',
    };
  }
}

/**
 * Prepare top-up transaction
 */
export async function prepareTopUpTransaction(
  userWallet: PublicKey,
  amount: number,
  connection?: Connection
): Promise<Transaction> {
  const conn = connection || solanaConnection;
  
  console.log('🔧 Preparing top-up transaction:', {
    userWallet: userWallet.toString(),
    amount,
  });

  try {
    // Get recipient address from environment
    const recipientAddress = process.env.RECIPIENT_WALLET_ADDRESS;
    if (!recipientAddress) {
      throw new Error('Recipient wallet address not configured');
    }

    const recipientWallet = new PublicKey(recipientAddress);

    // Validate the request first
    const validation = await validateTopUpRequest(userWallet, amount, conn);
    if (!validation.isValid) {
      throw new Error(validation.error || 'Invalid top-up request');
    }

    // Create the transfer transaction
    const transaction = await transferCopyTokens(
      userWallet,
      recipientWallet,
      amount,
      conn
    );

    console.log('✅ Top-up transaction prepared successfully');
    return transaction;
  } catch (error) {
    console.error('❌ Error preparing top-up transaction:', error);
    throw new Error(`Failed to prepare transaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get recipient wallet address
 */
export function getRecipientAddress(): string {
  const recipientAddress = process.env.RECIPIENT_WALLET_ADDRESS;
  if (!recipientAddress) {
    throw new Error('Recipient wallet address not configured');
  }
  return recipientAddress;
}

/**
 * Calculate top-up fee
 */
export function calculateTopUpFee(amount: number): number {
  const feePercentage = parseFloat(process.env.TOPUP_FEE_PERCENTAGE || '0.1');
  return amount * (feePercentage / 100);
}

/**
 * Get top-up limits
 */
export function getTopUpLimits() {
  return {
    minAmount: parseFloat(process.env.TOPUP_MIN_AMOUNT || '1'),
    maxAmount: parseFloat(process.env.TOPUP_MAX_AMOUNT || '10000'),
    feePercentage: parseFloat(process.env.TOPUP_FEE_PERCENTAGE || '0.1'),
  };
}

/**
 * Verify transaction signature
 */
export async function verifyTopUpTransaction(
  signature: string,
  expectedAmount: number,
  userWallet: PublicKey,
  connection?: Connection
): Promise<boolean> {
  const conn = connection || solanaConnection;
  
  console.log('🔍 Verifying top-up transaction:', {
    signature,
    expectedAmount,
    userWallet: userWallet.toString(),
  });

  try {
    // Get transaction details
    const transaction = await conn.getTransaction(signature, {
      commitment: 'confirmed',
    });

    if (!transaction) {
      console.error('❌ Transaction not found:', signature);
      return false;
    }

    if (transaction.meta?.err) {
      console.error('❌ Transaction failed:', transaction.meta.err);
      return false;
    }

    // Additional verification logic can be added here
    // For now, we'll consider it verified if the transaction exists and succeeded
    console.log('✅ Transaction verified successfully');
    return true;
  } catch (error) {
    console.error('❌ Error verifying transaction:', error);
    return false;
  }
}

/**
 * Format transaction signature for display
 */
export function formatTransactionSignature(signature: string): string {
  if (signature.length <= 16) return signature;
  return `${signature.slice(0, 8)}...${signature.slice(-8)}`;
}

/**
 * Get Solana explorer URL for transaction
 */
export function getTransactionExplorerUrl(signature: string, network: string = 'devnet'): string {
  const baseUrl = 'https://explorer.solana.com/tx';
  const cluster = network === 'mainnet-beta' ? '' : `?cluster=${network}`;
  return `${baseUrl}/${signature}${cluster}`;
}
